/**
 * Test script to verify cart business hours validation is working
 * 
 * This script helps you test that the cart properly prevents navigation
 * to checkout when business hours validation fails.
 * 
 * Run this in the browser console to test the functionality.
 */

function testCartBusinessHoursValidation() {
  console.log("🧪 TESTING: Cart Business Hours Validation");
  console.log("=" .repeat(60));

  // Instructions for testing
  console.log("\n📋 TESTING STEPS:");
  console.log("1. Open the cart (click the cart button)");
  console.log("2. Add some items to cart and select a box size");
  console.log("3. Look for these console logs:");
  console.log("   🛒 Cart - Component level validation: {...}");
  console.log("4. Check if the 'PLACE ORDER' button is:");
  console.log("   - DISABLED (gray) when business is closed");
  console.log("   - Shows red error message when business is closed");
  console.log("5. Try clicking 'PLACE ORDER' when business is closed");
  console.log("6. Verify it does NOT navigate to checkout page");

  console.log("\n🔍 WHAT TO LOOK FOR:");
  console.log("✅ Button disabled: bg-gray-300 text-gray-500 cursor-not-allowed");
  console.log("✅ Red error message: 'Business is currently closed...'");
  console.log("✅ Console log: 'Business validation failed, showing error and preventing order'");
  console.log("❌ Should NOT see: Navigation to /checkout");

  console.log("\n🧪 FORCE TEST SCENARIOS:");
  console.log("Run these functions to test different scenarios:");
  console.log("- window.testBusinessClosed() - Simulate business closed");
  console.log("- window.testBusinessOpen() - Simulate business open");

  return {
    instructions: "Follow the testing steps above",
    expectedBehavior: "Cart should prevent checkout when business is closed"
  };
}

// Function to simulate business closed scenario
function testBusinessClosed() {
  console.log("🔴 SIMULATING: Business Closed Scenario");
  
  // Create test timing data where business is closed
  const closedTimingData = {
    hours: {
      "1": [{ alias: "Monday", timing: [[600, 720]] }], // 10 AM - 12 PM (closed now if after 12 PM)
      "2": [{ alias: "Tuesday", timing: [[0, 0]] }], // Closed all day
      "3": [{ alias: "Wednesday", timing: [[0, 0]] }], // Closed all day
      "4": [{ alias: "Thursday", timing: [[0, 0]] }], // Closed all day
      "5": [{ alias: "Friday", timing: [[0, 0]] }], // Closed all day
      "6": [{ alias: "Saturday", timing: [[0, 0]] }], // Closed all day
      "7": [{ alias: "Sunday", timing: [[0, 0]] }] // Closed all day
    }
  };

  console.log("📊 Test timing data (business closed):", closedTimingData);
  
  // Test the validation function
  if (typeof window.debugMultipleTimingSlots === 'function') {
    const result = window.debugMultipleTimingSlots(closedTimingData);
    console.log("🧪 Validation result:", result);
  }

  console.log("\n✅ Expected behavior:");
  console.log("- Cart button should be DISABLED");
  console.log("- Red error message should appear");
  console.log("- Clicking button should NOT navigate to checkout");
  
  return closedTimingData;
}

// Function to simulate business open scenario
function testBusinessOpen() {
  console.log("🟢 SIMULATING: Business Open Scenario");
  
  // Create test timing data where business is open 24/7
  const openTimingData = {
    hours: {
      "1": [{ alias: "Monday", timing: [] }], // 24/7
      "2": [{ alias: "Tuesday", timing: [] }], // 24/7
      "3": [{ alias: "Wednesday", timing: [] }], // 24/7
      "4": [{ alias: "Thursday", timing: [] }], // 24/7
      "5": [{ alias: "Friday", timing: [] }], // 24/7
      "6": [{ alias: "Saturday", timing: [] }], // 24/7
      "7": [{ alias: "Sunday", timing: [] }] // 24/7
    }
  };

  console.log("📊 Test timing data (business open 24/7):", openTimingData);
  
  // Test the validation function
  if (typeof window.debugMultipleTimingSlots === 'function') {
    const result = window.debugMultipleTimingSlots(openTimingData);
    console.log("🧪 Validation result:", result);
  }

  console.log("\n✅ Expected behavior:");
  console.log("- Cart button should be ENABLED");
  console.log("- No error message should appear");
  console.log("- Clicking button should navigate to checkout (if other validations pass)");
  
  return openTimingData;
}

// Function to check current cart state
function checkCurrentCartState() {
  console.log("🔍 CHECKING: Current Cart State");
  
  // Try to find cart elements
  const cartButton = document.querySelector('button[class*="PLACE ORDER"], button:contains("PLACE ORDER")');
  const errorMessages = document.querySelectorAll('[class*="bg-red-50"], [class*="text-red-700"]');
  
  console.log("🛒 Cart button found:", !!cartButton);
  if (cartButton) {
    console.log("🛒 Button disabled:", cartButton.disabled);
    console.log("🛒 Button classes:", cartButton.className);
  }
  
  console.log("🚨 Error messages found:", errorMessages.length);
  errorMessages.forEach((msg, index) => {
    console.log(`🚨 Error ${index + 1}:`, msg.textContent);
  });

  return {
    cartButtonFound: !!cartButton,
    cartButtonDisabled: cartButton?.disabled,
    errorMessagesCount: errorMessages.length
  };
}

// Make functions available globally
if (typeof window !== "undefined") {
  window.testCartBusinessHoursValidation = testCartBusinessHoursValidation;
  window.testBusinessClosed = testBusinessClosed;
  window.testBusinessOpen = testBusinessOpen;
  window.checkCurrentCartState = checkCurrentCartState;
  
  console.log("🔧 Cart business hours validation test functions loaded!");
  console.log("Run: window.testCartBusinessHoursValidation()");
}

// Auto-run the main test function
testCartBusinessHoursValidation();
