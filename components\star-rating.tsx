"use client"

type StarRatingProps = {
  rating: number
  maxRating?: number
  starSize?: number
  fillColor?: string
  emptyColor?: string
  className?: string
}

export default function StarRating({
  rating,
  maxRating = 5,
  starSize = 24,
  fillColor = "#FFA800",
  emptyColor = "#d1d5db",
  className = "",
}: StarRatingProps) {
  // Create an array of length maxRating
  const stars = Array.from({ length: maxRating }, (_, i) => {
    // Calculate how filled this star should be (0 to 1)
    const fillPercentage = Math.max(0, Math.min(1, rating - i));
    
    return (
      <div key={i} className="relative inline-block">
        {/* Empty star (background) */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke={emptyColor}
          width={starSize}
          height={starSize}
          className="absolute top-0 left-0"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
          />
        </svg>
        
        {/* Filled star (foreground with clip-path for partial fill) */}
        {fillPercentage > 0 && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill={fillColor}
            stroke={fillColor}
            width={starSize}
            height={starSize}
            style={{
              clipPath: `inset(0 ${100 - fillPercentage * 100}% 0 0)`,
            }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
            />
          </svg>
        )}
      </div>
    );
  });

  return <div className={`flex ${className}`}>{stars}</div>;
}
