"use client"

import { usePathname, useRouter } from "next/navigation"
import type { mapProductToMenuItem } from "@/services/api"
import { Suspense, useEffect, useRef } from "react"
import MenuItemCard from "@/components/menu-item-card"
import InitialBoxSizeSelector from "@/components/initial-box-size-selector"
import MenuPageSkeleton from "@/components/menu-page-skeleton"
import { useToast } from "@/context/toast-context"

const responsiveStyles = `
 @media (max-width: 1024px) {
    .fixed_btn a {
      font-size: 18px !important;
      width: 155px !important;
      height: 42px !important;
    }
  }


  @media (max-width: 768px) {
    .fixed_btn a {
      height: 43px !important;
      width: 148px !important;
    }
  }
    @media (max-width: 540px) {
    .fixed_btn a {
      width: 132px !important;
      height: 40px !important;
      font-size: 16px !important;
    }
  }
`

interface MenuContentProps {
  menuItems: ReturnType<typeof mapProductToMenuItem>[]
  error: string | null
}

export default function MenuContent({ menuItems, error }: MenuContentProps) {
  const pathname = usePathname()
  const router = useRouter()
  const isOurMenu = pathname === "/our-menu"
  const fixedBtnRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  // Handle button position on scroll
  useEffect(() => {
    if (!isOurMenu) return

    const fixedBtn = fixedBtnRef.current
    const footer = document.querySelector(".footer-section")

    if (!fixedBtn || !footer) return

    function checkFooterPosition() {
      const footerRect = footer.getBoundingClientRect()
      const buttonHeight = fixedBtn.offsetHeight

      // When footer comes into view
      if (footerRect.top < window.innerHeight) {
        // Position the button above the footer
        fixedBtn.classList.add("sticky-above-footer")
        fixedBtn.style.bottom = window.innerHeight - footerRect.top + 20 + "px"
      } else {
        // Return to normal fixed position
        fixedBtn.classList.remove("sticky-above-footer")
        fixedBtn.style.bottom = "20px"
      }
    }

    // Initial check
    checkFooterPosition()

    // Add event listeners
    window.addEventListener("scroll", checkFooterPosition)
    window.addEventListener("resize", checkFooterPosition)

    // Cleanup function
    return () => {
      window.removeEventListener("scroll", checkFooterPosition)
      window.removeEventListener("resize", checkFooterPosition)
    }
  }, [isOurMenu])

  return (
    <main className="pb-[120px]">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="page_heading mt-8 md:mt-10">
          <h4>{isOurMenu ? "OUR MENU" : "ORDERING MENU"}</h4>
        </div>
        {/* Only show the InitialBoxSizeSelector on the /menu route */}
        {!isOurMenu && <InitialBoxSizeSelector />}

        <Suspense fallback={<MenuPageSkeleton />}>
          {error ? (
            <div className="text-center text-red-500 py-10">{error}</div>
          ) : menuItems.length === 0 ? (
            <div className="text-center py-10">No menu items available at the moment.</div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-x-4 gap-y-10 md:gap-x-6">
              {menuItems.map((item) => (
                <div key={item.id} id={`item-${item.id}`}>
                  <MenuItemCard
                    id={item.id}
                    name={item.name}
                    image={item.image}
                    price={item.price}
                    isVeg={item.isVeg}
                    rating={item.rating}
                    inStock={item.inStock}
                    hideAddToCart={isOurMenu}
                  />
                </div>
              ))}
            </div>
          )}
        </Suspense>
      </div>

      {/* ORDER NOW button for /our-menu route - sticky at bottom-right */}
      {isOurMenu && (
        <div ref={fixedBtnRef} className="fixed bottom-6 right-6 z-40 fixed_btn">
          <a
            onClick={(e) => {
              e.preventDefault()
              // Show toast message
              showToast({
                message: "Almost there! Just enter your address to unlock our menu.",
                type: "error",
                duration: 5000,
              })

              // Navigate to home page
              router.push("/")
            }}
            href="/"
            className="font-poppins text-[23px] font-normal leading-[23px] tracking-[0em] flex items-center justify-center w-[208px] h-[52px] rounded-[2px] capitalize bg-black text-white relative cursor-pointer shadow-[0px_48px_100px_0px_rgba(17,12,46,0.15)]"
          >
            ORDER NOW
          </a>
        </div>
      )}
      <style jsx>{responsiveStyles}</style>
    </main>
  )
}
