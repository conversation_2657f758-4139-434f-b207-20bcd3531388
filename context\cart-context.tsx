"use client";

import type React from "react";
import { createContext, useContext, useState, useEffect, useRef } from "react";
import { useToast } from "./toast-context";
import {
  addToCart,
  removeFromCart,
  deleteFromCart,
  getUniqueOrderId,
} from "@/services/cart-api";
import { trackAddToCart } from "@/utils/analytics";

export type CartItem = {
  id: number;
  name: string;
  price: number;
  quantity: number;
  image: string;
  isVeg: boolean;
};

export type BoxSize = 6 | 12 | null;

// Define the possible cart operations
export type CartOperation = "add" | "remove" | "update" | "clear" | null;

// Add these new types for address details
export type AddressDetails = {
  address: string;
  city: string;
  appartment: string;
  area: string;
  country: string;
  postalCode: string;
};

export type UserLocation = {
  lat: string;
  lng: string;
};

// Add a new type for order schedule
export type OrderSchedule = {
  date: string;
  time: string;
};

// Add addressError to the CartContextType
type CartContextType = {
  items: CartItem[];
  addItem: (item: Omit<CartItem, "quantity">) => void;
  addItemDirectly: (item: Omit<CartItem, "quantity">) => void;
  removeItem: (id: number) => void;
  updateQuantity: (id: number, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  totalPrice: number;
  boxSize: BoxSize;
  setBoxSize: (size: BoxSize) => void;
  checkBoxSize: () => boolean;
  remainingSlots: number;
  isBoxFull: boolean;
  showBoxSelector: boolean;
  setShowBoxSelector: (show: boolean) => void;
  pendingItem: Omit<CartItem, "quantity"> | null;
  setPendingItem: (item: Omit<CartItem, "quantity"> | null) => void;
  orderType: "delivery" | "pickup";
  setOrderType: (type: "delivery" | "pickup") => void;
  deliveryTiming: string;
  setDeliveryTiming: (timing: string) => void;
  deliveryAddress: string;
  setDeliveryAddress: (address: string) => void;
  addressDetails: AddressDetails;
  setAddressDetails: (details: AddressDetails) => void;
  userLocation: UserLocation;
  setUserLocation: (location: UserLocation) => void;
  isLoading: boolean;
  loadingItemId: number | null;
  loadingOperation: CartOperation;
  scheduledDateTime: string | null;
  setScheduledDateTime: (dateTime: string | null) => void;
  orderSchedule: OrderSchedule | null;
  setOrderSchedule: (schedule: OrderSchedule | null) => void;
  addressError: string | null;
  setAddressError: (error: string | null) => void;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

// Add the new state variable in the CartProvider
export const CartProvider = ({ children }: { children: React.ReactNode }) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [boxSize, setBoxSize] = useState<BoxSize>(null);
  const [showBoxSelector, setShowBoxSelector] = useState(false);
  const [pendingItem, setPendingItem] = useState<Omit<
    CartItem,
    "quantity"
  > | null>(null);
  const [orderType, setOrderType] = useState<"delivery" | "pickup">("delivery");
  const [deliveryTiming, setDeliveryTiming] = useState<string>("now");
  const [deliveryAddress, setDeliveryAddress] = useState<string>("");
  const [addressDetails, setAddressDetails] = useState<AddressDetails>({
    address: "",
    city: "",
    appartment: "",
    area: "",
    country: "",
    postalCode: "",
  });
  const [userLocation, setUserLocation] = useState<UserLocation>({
    lat: "",
    lng: "",
  });
  const [scheduledDateTime, setScheduledDateTime] = useState<string | null>(
    null
  );
  const [orderSchedule, setOrderSchedule] = useState<OrderSchedule | null>(
    null
  );
  const [addressError, setAddressError] = useState<string | null>(null);
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingItemId, setLoadingItemId] = useState<number | null>(null);
  const [loadingOperation, setLoadingOperation] = useState<CartOperation>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const isAddingItem = useRef(false);
  const isFirstLoad = useRef(true);
  const savedTimingRef = useRef<string | null>(null);

  // Initialize unique order ID on mount
  useEffect(() => {
    // Just call this to ensure we have a unique order ID
    getUniqueOrderId();
  }, []);

  // Add loading of address details from localStorage
  useEffect(() => {
    // Only run this once
    if (isInitialized) return;

    try {
      // Load delivery timing from localStorage first
      if (typeof window !== "undefined") {
        const savedTiming = localStorage.getItem("ezeats-delivery-timing");
        if (savedTiming) {
          savedTimingRef.current = savedTiming;
          setDeliveryTiming(savedTiming);
        }
      }

      const savedCart = localStorage.getItem("cart");
      const savedBoxSize = localStorage.getItem("boxSize");
      const savedOrderType = localStorage.getItem("orderType");
      const savedDeliveryAddress = localStorage.getItem("deliveryAddress");
      const savedAddressDetails = localStorage.getItem("addressDetails");
      const savedUserLocation = localStorage.getItem("userLocation");
      const savedOrderSchedule = localStorage.getItem("orderSchedule");

      // Load existing values
      if (savedCart) {
        try {
          setItems(JSON.parse(savedCart));
        } catch (e) {
          console.error("Failed to parse cart from localStorage", e);
        }
      }

      if (savedBoxSize) {
        try {
          const parsedSize = JSON.parse(savedBoxSize) as BoxSize;
          setBoxSize(parsedSize);
        } catch (e) {
          console.error("Failed to parse box size from localStorage", e);
        }
      }

      if (savedOrderType) {
        try {
          const parsedType = JSON.parse(savedOrderType) as
            | "delivery"
            | "pickup";
          setOrderType(parsedType);
        } catch (e) {
          console.error("Failed to parse order type from localStorage", e);
        }
      }

      // REMOVED: Always set delivery timing to "now" on initial load, regardless of what's in localStorage
      // This line was causing the delivery timing to be reset to "now" on page load
      // setDeliveryTiming("now")

      if (savedDeliveryAddress) {
        try {
          setDeliveryAddress(savedDeliveryAddress);
        } catch (e) {
          console.error(
            "Failed to parse delivery address from localStorage",
            e
          );
        }
      }

      if (savedAddressDetails) {
        try {
          setAddressDetails(JSON.parse(savedAddressDetails));
        } catch (e) {
          console.error("Failed to parse address details from localStorage", e);
        }
      }

      if (savedUserLocation) {
        try {
          setUserLocation(JSON.parse(savedUserLocation));
        } catch (e) {
          console.error("Failed to parse user location from localStorage", e);
        }
      }

      // Don't restore scheduledDateTime on page load to prevent the modal from showing
      setScheduledDateTime(null);

      // Load order schedule if available
      if (savedOrderSchedule) {
        try {
          const parsedSchedule = JSON.parse(
            savedOrderSchedule
          ) as OrderSchedule;
          setOrderSchedule(parsedSchedule);

          // If we have a valid schedule, set delivery timing to "later"
          if (parsedSchedule && parsedSchedule.date && parsedSchedule.time) {
            // Only set to "later" if we don't have a saved timing
            if (!savedTimingRef.current) {
              setDeliveryTiming("later");
            }
          }
        } catch (e) {
          console.error("Failed to parse order schedule from localStorage", e);
        }
      }

      setIsInitialized(true);
    } catch (error) {
      console.error("Error initializing cart from localStorage:", error);
      setIsInitialized(true);
    }
  }, [isInitialized]);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (items.length > 0) {
      localStorage.setItem("cart", JSON.stringify(items));
    } else {
      localStorage.removeItem("cart");
    }
  }, [items]);

  // Save box size to localStorage whenever it changes
  useEffect(() => {
    if (boxSize) {
      localStorage.setItem("boxSize", JSON.stringify(boxSize));
    } else {
      localStorage.removeItem("boxSize");
    }
  }, [boxSize]);

  // Save order type to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("orderType", JSON.stringify(orderType));
  }, [orderType]);

  // Save delivery timing to localStorage whenever it changes
  useEffect(() => {
    if (deliveryTiming) {
      localStorage.setItem("ezeats-delivery-timing", deliveryTiming);
    }
  }, [deliveryTiming]);

  // Save delivery address to localStorage whenever it changes
  useEffect(() => {
    if (deliveryAddress) {
      localStorage.setItem("deliveryAddress", deliveryAddress);
    } else {
      localStorage.removeItem("deliveryAddress");
    }
  }, [deliveryAddress]);

  // Save address details to localStorage whenever they change
  useEffect(() => {
    if (addressDetails.address) {
      localStorage.setItem("addressDetails", JSON.stringify(addressDetails));
    }
  }, [addressDetails]);

  // Save user location to localStorage whenever it changes
  useEffect(() => {
    if (userLocation.lat && userLocation.lng) {
      localStorage.setItem("userLocation", JSON.stringify(userLocation));
    }
  }, [userLocation]);

  // Save scheduled date/time to localStorage whenever it changes
  useEffect(() => {
    if (scheduledDateTime) {
      localStorage.setItem("scheduledDateTime", scheduledDateTime);
    } else {
      localStorage.removeItem("scheduledDateTime");
    }
  }, [scheduledDateTime]);

  // Save order schedule to localStorage whenever it changes
  useEffect(() => {
    if (orderSchedule) {
      localStorage.setItem("orderSchedule", JSON.stringify(orderSchedule));
    } else {
      localStorage.removeItem("orderSchedule");
    }
  }, [orderSchedule]);

  // Update the useEffect that resets scheduledDateTime and orderSchedule when delivery timing changes

  // Add this effect to reset scheduledDateTime and orderSchedule when delivery timing changes
  // We need to make sure this doesn't interfere with our scheduling process
  useEffect(() => {
    // Skip this effect during the first render/initialization
    if (isFirstLoad.current) {
      isFirstLoad.current = false;
      return;
    }

    // Only reset scheduled date if changing to "now" (immediate delivery)
    if (deliveryTiming === "now") {
      setScheduledDateTime(null);
      setOrderSchedule(null);
    }
  }, [deliveryTiming]);

  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  const remainingSlots = boxSize ? boxSize - totalItems : 0;
  const isBoxFull = boxSize ? totalItems >= boxSize : false;

  // Helper function to check if API response is successful
  const isSuccessResponse = (response: any): boolean => {
    // Handle null or undefined response
    if (!response) return false;

    // Check for different possible success indicators in the response
    return (
      (typeof response.status === "string" &&
        response.status.toLowerCase() === "success") ||
      response.status === 200 ||
      response.success === true ||
      (typeof response.message === "string" &&
        response.message.toLowerCase().includes("success"))
    );
  };

  // Helper function to set loading state
  const setLoading = (itemId: number | null, operation: CartOperation) => {
    setLoadingItemId(itemId);
    setLoadingOperation(operation);
    setIsLoading(true);
  };

  // Helper function to clear loading state
  const clearLoading = () => {
    setLoadingItemId(null);
    setLoadingOperation(null);
    setIsLoading(false);
  };

  const addItem = async (newItem: Omit<CartItem, "quantity">) => {
    // Prevent duplicate calls during processing
    if (isAddingItem.current) return;
    isAddingItem.current = true;

    // Set loading state for this item
    setLoading(newItem.id, "add");

    // If no box size is selected, show the selector and save the pending item
    if (!boxSize) {
      setPendingItem(newItem);
      setShowBoxSelector(true);
      isAddingItem.current = false;
      clearLoading();
      return;
    }

    // If box is already full, show toast notification
    if (isBoxFull) {
      showToast({
        message: `Your ${boxSize}-meal box is full. Please remove some items or change your box size`,
        type: "warning",
        duration: 4000,
      });
      isAddingItem.current = false;
      clearLoading();
      return;
    }

    try {
      // Continue with normal item addition
      const existingItem = items.find((item) => item.id === newItem.id);

      if (existingItem) {
        // If adding would exceed box size, show toast notification
        if (
          totalItems - existingItem.quantity + existingItem.quantity + 1 >
          (boxSize || 0)
        ) {
          showToast({
            message: "You have reached the meal limit for your box size",
            type: "error",
            duration: 4000,
          });
          isAddingItem.current = false;
          clearLoading();
          return;
        }

        // If item already exists, increase quantity
        const updatedItem = { ...existingItem, quantity: 1 }; // Always use quantity 1 for API

        try {
          // Update the API first
          const response = await addToCart(updatedItem, orderType);

          // Check if response indicates success
          if (isSuccessResponse(response)) {
            // Then update local state
            setItems((prevItems) =>
              prevItems.map((item) =>
                item.id === newItem.id
                  ? { ...item, quantity: item.quantity + 1 }
                  : item
              )
            );

            // Show success toast
            showToast({
              message: "Item added in the cart",
              type: "success",
              duration: 3000,
            });

            // Track add to cart event
            trackAddToCart(
              {
                id: newItem.id,
                name: newItem.name,
                price: newItem.price,
              },
              1
            );
          } else {
            console.warn("API response did not indicate success:", response);
            // Still update the local state to maintain user experience
            setItems((prevItems) =>
              prevItems.map((item) =>
                item.id === newItem.id
                  ? { ...item, quantity: item.quantity + 1 }
                  : item
              )
            );

            // Show success toast anyway for better UX
            showToast({
              message: "Item added in the cart",
              type: "success",
              duration: 3000,
            });
          }
        } catch (apiError) {
          console.error("API error when adding item:", apiError);
          // Still update the local state to maintain user experience
          setItems((prevItems) =>
            prevItems.map((item) =>
              item.id === newItem.id
                ? { ...item, quantity: item.quantity + 1 }
                : item
            )
          );

          showToast({
            message: "Item added in the cart (offline mode)",
            type: "success",
            duration: 3000,
          });
        }
      } else {
        // Otherwise add new item with quantity 1
        const newCartItem = { ...newItem, quantity: 1 };

        try {
          // Update the API first
          const response = await addToCart(newCartItem, orderType);

          // Check if response indicates success
          if (isSuccessResponse(response)) {
            // Then update local state
            setItems((prevItems) => [...prevItems, newCartItem]);

            // Show success toast
            showToast({
              message: "Item added in the cart",
              type: "success",
              duration: 3000,
            });

            // Track add to cart event
            trackAddToCart(
              {
                id: newItem.id,
                name: newItem.name,
                price: newItem.price,
              },
              1
            );
          } else {
            console.warn("API response did not indicate success:", response);
            // Still update the local state to maintain user experience
            setItems((prevItems) => [...prevItems, newCartItem]);

            // Show success toast anyway for better UX
            showToast({
              message: "Item added in the cart",
              type: "success",
              duration: 3000,
            });
          }
        } catch (apiError) {
          console.error("API error when adding new item:", apiError);
          // Still update the local state to maintain user experience
          setItems((prevItems) => [...prevItems, newCartItem]);

          showToast({
            message: "Item added in the cart (offline mode)",
            type: "success",
            duration: 3000,
          });
        }
      }
    } catch (error) {
      console.error("Error adding item to cart:", error);
      showToast({
        message: "Failed to add item to cart. Please try again",
        type: "error",
        duration: 4000,
      });
    } finally {
      // Reset loading states
      clearLoading();

      // Reset the flag after a short delay
      setTimeout(() => {
        isAddingItem.current = false;
      }, 100);
    }
  };

  const addItemDirectly = async (newItem: Omit<CartItem, "quantity">) => {
    // Skip all the box size checks since we're coming from the box selector
    // This function is only called after a box size has been selected
    setLoading(newItem.id, "add");

    try {
      const existingItem = items.find((item) => item.id === newItem.id);

      if (existingItem) {
        // If item already exists, increase quantity
        const updatedItem = { ...existingItem, quantity: 1 }; // Always use quantity 1 for API

        // Update the API first
        const response = await addToCart(updatedItem, orderType);

        // Update local state regardless of API response to maintain user experience
        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === newItem.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          )
        );

        // Show success toast
        showToast({
          message: "Item added in the cart",
          type: "success",
          duration: 3000,
        });

        // Track add to cart event
        trackAddToCart(
          {
            id: newItem.id,
            name: newItem.name,
            price: newItem.price,
          },
          1
        );
      } else {
        // Otherwise add new item with quantity 1
        const newCartItem = { ...newItem, quantity: 1 };

        // Update the API first
        const response = await addToCart(newCartItem, orderType);

        // Update local state regardless of API response to maintain user experience
        setItems((prevItems) => [...prevItems, newCartItem]);

        // Show success toast
        showToast({
          message: "Item added in the cart",
          type: "success",
          duration: 3000,
        });

        // Track add to cart event
        trackAddToCart(
          {
            id: newItem.id,
            name: newItem.name,
            price: newItem.price,
          },
          1
        );
      }
    } catch (error) {
      console.error("Error adding item directly to cart:", error);
      // Still proceed with local state update to maintain user experience
      const newCartItem = { ...newItem, quantity: 1 };
      setItems((prevItems) => {
        const existingItem = prevItems.find((item) => item.id === newItem.id);
        if (existingItem) {
          return prevItems.map((item) =>
            item.id === newItem.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          );
        } else {
          return [...prevItems, newCartItem];
        }
      });

      showToast({
        message:
          "There was an issue syncing with the server, but your item was added locally",
        type: "warning",
        duration: 4000,
      });
    } finally {
      clearLoading();
    }
  };

  const removeItem = async (id: number) => {
    setLoading(id, "delete");

    try {
      const itemToRemove = items.find((item) => item.id === id);

      if (itemToRemove) {
        // Update the API first - always use quantity 1 for API
        const apiItem = { ...itemToRemove, quantity: 1 };

        try {
          const response = await deleteFromCart(apiItem, orderType);
        } catch (apiError) {
          console.error("API error when removing item:", apiError);
          // Continue with local state update even if API fails
        }

        // Update local state regardless of API response
        setItems((prevItems) => prevItems.filter((item) => item.id !== id));

        // If cart is empty after removal, clear localStorage
        if (items.length === 1) {
          localStorage.removeItem("cart");
        }
      }
    } catch (error) {
      console.error("Error removing item from cart:", error);
      showToast({
        message: "Failed to remove item from cart. Please try again",
        type: "error",
        duration: 4000,
      });
    } finally {
      clearLoading();
    }
  };

  const updateQuantity = async (id: number, quantity: number) => {
    // Determine if this is an increment or decrement operation
    const currentItem = items.find((item) => item.id === id);
    if (!currentItem) return;

    const operation = quantity > currentItem.quantity ? "add" : "remove";
    setLoading(id, operation);

    try {
      if (quantity <= 0) {
        await removeItem(id);
        return;
      }

      // Check if the new quantity would exceed the box size
      const otherItemsTotal = totalItems - currentItem.quantity;

      if (boxSize && otherItemsTotal + quantity > boxSize) {
        // Different messages based on box size
        const message =
          boxSize === 6
            ? "To add more meals, please update your cart to 12 meals"
            : "You have reached your maximum meal limit for your box size, please proceed to checkout.";

        showToast({
          message,
          type: "error",
          duration: 4000,
        });
        clearLoading();
        return;
      }

      // Calculate the difference in quantity
      const quantityDiff = quantity - currentItem.quantity;
      let apiSuccess = true;

      // If quantity is increasing, call addToCart multiple times
      if (quantityDiff > 0) {
        const updatedItem = { ...currentItem, quantity: 1 }; // Always use quantity 1 for API
        for (let i = 0; i < quantityDiff; i++) {
          try {
            const response = await addToCart(updatedItem, orderType);
          } catch (apiError) {
            console.error(
              `API error on add attempt ${i + 1}/${quantityDiff}:`,
              apiError
            );
            apiSuccess = false;
          }
        }

        // Show success toast for increasing quantity
        showToast({
          message: "Item added in the cart",
          type: "success",
          duration: 3000,
        });

        // Track add to cart event for quantity increase
        trackAddToCart(
          {
            id: currentItem.id,
            name: currentItem.name,
            price: currentItem.price,
          },
          quantityDiff
        );
      }
      // If quantity is decreasing, call removeFromCart multiple times
      else if (quantityDiff < 0) {
        const updatedItem = { ...currentItem, quantity: 1 }; // Always use quantity 1 for API
        for (let i = 0; i < Math.abs(quantityDiff); i++) {
          try {
            const response = await removeFromCart(updatedItem, orderType);
          } catch (apiError) {
            console.error(
              `API error on remove attempt ${i + 1}/${Math.abs(quantityDiff)}:`,
              apiError
            );
            apiSuccess = false;
          }
        }

        // Remove the toast message for decreasing quantity
      }

      // Update local state regardless of API success to maintain user experience
      setItems((prevItems) =>
        prevItems.map((item) => (item.id === id ? { ...item, quantity } : item))
      );

      if (!apiSuccess) {
        showToast({
          message:
            "There was an issue syncing with the server, but your cart was updated locally",
          type: "warning",
          duration: 4000,
        });
      }
    } catch (error) {
      console.error("Error updating item quantity:", error);
      showToast({
        message: "Failed to update item quantity. Please try again",
        type: "error",
        duration: 4000,
      });
    } finally {
      clearLoading();
    }
  };

  const clearCart = async () => {
    setLoading(null, "clear");

    try {
      let apiSuccess = true;

      // Delete each item from the API
      for (const item of items) {
        try {
          const apiItem = { ...item, quantity: 1 }; // Always use quantity 1 for API
          const response = await deleteFromCart(apiItem, orderType);
        } catch (apiError) {
          console.error(`API error when clearing item ${item.id}:`, apiError);
          apiSuccess = false;
        }
      }

      // Clear local state regardless of API success
      setItems([]);
      localStorage.removeItem("cart");

      // Show success toast
      showToast({
        message: "Your cart has been cleared",
        type: "warning",
        duration: 3000,
      });

      if (!apiSuccess) {
        showToast({
          message:
            "There was an issue syncing with the server, but your cart was cleared locally",
          type: "warning",
          duration: 4000,
        });
      }
    } catch (error) {
      console.error("Error clearing cart:", error);
      showToast({
        message: "Failed to clear cart. Please try again",
        type: "error",
        duration: 4000,
      });
    } finally {
      clearLoading();
    }
  };

  const totalPrice = items.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );

  // Add a new function to check if a box size is selected
  const checkBoxSize = () => {
    return boxSize !== null;
  };

  // Include the new state variable in the context value
  return (
    <CartContext.Provider
      value={{
        items,
        addItem,
        addItemDirectly,
        removeItem,
        updateQuantity,
        clearCart,
        totalItems,
        totalPrice,
        boxSize,
        setBoxSize,
        checkBoxSize,
        remainingSlots,
        isBoxFull,
        showBoxSelector,
        setShowBoxSelector,
        pendingItem,
        setPendingItem,
        orderType,
        setOrderType,
        deliveryTiming,
        setDeliveryTiming,
        deliveryAddress,
        setDeliveryAddress,
        addressDetails,
        setAddressDetails,
        userLocation,
        setUserLocation,
        isLoading,
        loadingItemId,
        loadingOperation,
        scheduledDateTime,
        setScheduledDateTime,
        orderSchedule,
        setOrderSchedule,
        addressError,
        setAddressError,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
};
