/**
 * Test script for timezone offset business hours validation
 * 
 * This script tests the business hours system with timezone offsets
 * like "-04:00", "+05:30" etc. to ensure validation uses business offset
 * instead of user's local time.
 * 
 * Run this in the browser console to test the functionality.
 */

function testTimezoneOffsetValidation() {
  console.log("🌍 TESTING: Timezone Offset Business Hours Validation");
  console.log("=" .repeat(60));

  // Test different timezone offsets
  const testOffsets = [
    "-04:00",  // EDT (Eastern Daylight Time)
    "-05:00",  // EST (Eastern Standard Time) 
    "-07:00",  // PDT (Pacific Daylight Time)
    "-08:00",  // PST (Pacific Standard Time)
    "+00:00",  // UTC
    "+05:30",  // IST (India Standard Time)
    "+09:00",  // JST (Japan Standard Time)
    "-03:00",  // BRT (Brazil Time)
  ];

  console.log("\n📋 TESTING DIFFERENT TIMEZONE OFFSETS:");
  
  testOffsets.forEach(offset => {
    console.log(`\n🌍 Testing offset: ${offset}`);
    
    try {
      // Test with debug function if available
      if (typeof window !== "undefined" && window.debugTiming) {
        const result = window.debugTiming(offset);
        console.log(`   Debug result:`, result);
      } else {
        // Manual calculation
        const now = new Date();
        const userTime = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
        
        // Parse offset
        const offsetMatch = offset.match(/^([+-])(\d{2}):(\d{2})$/);
        if (offsetMatch) {
          const [, sign, hours, minutes] = offsetMatch;
          const offsetMinutes = (parseInt(hours) * 60 + parseInt(minutes)) * (sign === '+' ? 1 : -1);
          const businessTime = new Date(now.getTime() + offsetMinutes * 60 * 1000);
          const businessTimeStr = `${businessTime.getHours()}:${businessTime.getMinutes().toString().padStart(2, '0')}`;
          
          console.log(`   User local time: ${userTime}`);
          console.log(`   Business time (${offset}): ${businessTimeStr}`);
          console.log(`   Offset applied: ${offsetMinutes} minutes`);
        }
      }
      
    } catch (error) {
      console.error(`   ❌ Error testing ${offset}:`, error.message);
    }
  });

  console.log("\n🧪 TESTING BUSINESS HOURS WITH OFFSET:");
  console.log("1. Check console logs for offset comparison");
  console.log("2. Look for 'User local time' vs 'Business time (offset X)' logs");
  console.log("3. Verify business hours validation uses business offset");

  return {
    testOffsets,
    instructions: "Check console logs for offset validation details"
  };
}

// Function to test specific offset scenario
function testSpecificOffset(offset, businessHours) {
  console.log(`🌍 TESTING SPECIFIC OFFSET: ${offset}`);
  console.log("=" .repeat(50));

  try {
    // Parse offset
    const offsetMatch = offset.match(/^([+-])(\d{2}):(\d{2})$/);
    if (!offsetMatch) {
      console.error(`❌ Invalid offset format: ${offset}`);
      return { error: "Invalid offset format" };
    }

    const [, sign, hours, minutes] = offsetMatch;
    const offsetMinutes = (parseInt(hours) * 60 + parseInt(minutes)) * (sign === '+' ? 1 : -1);
    
    // Get current times
    const userNow = new Date();
    const businessTime = new Date(userNow.getTime() + offsetMinutes * 60 * 1000);
    
    console.log(`👤 User local time: ${userNow.toLocaleTimeString()}`);
    console.log(`🏢 Business time (${offset}): ${businessTime.toLocaleTimeString()}`);
    console.log(`⏰ Offset applied: ${offsetMinutes} minutes (${(offsetMinutes/60).toFixed(1)} hours)`);
    
    // Test business hours if provided
    if (businessHours) {
      const businessMinutes = businessTime.getHours() * 60 + businessTime.getMinutes();
      const dayOfWeek = businessTime.getDay() === 0 ? 7 : businessTime.getDay();
      
      console.log(`📅 Business day: ${dayOfWeek} (${['Sun','Mon','Tue','Wed','Thu','Fri','Sat'][businessTime.getDay()]})`);
      console.log(`🕐 Business minutes: ${businessMinutes}`);
      
      // Check if business should be open
      const dayHours = businessHours[dayOfWeek.toString()];
      if (dayHours && dayHours.length > 0) {
        let isOpen = false;
        for (const slot of dayHours) {
          if (!slot.timing || slot.timing.length === 0) {
            isOpen = true;
            console.log(`✅ Business is open 24/7`);
            break;
          }
          
          for (const [start, end] of slot.timing) {
            if (start === end) continue;
            if (businessMinutes >= start && businessMinutes <= end) {
              isOpen = true;
              console.log(`✅ Business is OPEN (${Math.floor(start/60)}:${(start%60).toString().padStart(2,'0')} - ${Math.floor(end/60)}:${(end%60).toString().padStart(2,'0')})`);
              break;
            }
          }
        }
        
        if (!isOpen) {
          console.log(`❌ Business is CLOSED`);
          dayHours.forEach((slot, i) => {
            if (slot.timing && slot.timing.length > 0) {
              slot.timing.forEach(([start, end]) => {
                if (start !== end) {
                  console.log(`   Hours: ${Math.floor(start/60)}:${(start%60).toString().padStart(2,'0')} - ${Math.floor(end/60)}:${(end%60).toString().padStart(2,'0')}`);
                }
              });
            }
          });
        }
      } else {
        console.log(`❌ No business hours for this day`);
      }
    }
    
    return {
      userTime: userNow,
      businessTime: businessTime,
      offset: offset,
      offsetMinutes: offsetMinutes
    };
    
  } catch (error) {
    console.error(`💥 Error testing offset ${offset}:`, error);
    return { error: error.message };
  }
}

// Function to compare validation results between local and business offset
function compareOffsetValidation(offset) {
  console.log(`🔍 COMPARING VALIDATION: Local vs ${offset}`);
  console.log("=" .repeat(50));

  // Sample business hours (9 AM - 5 PM)
  const sampleHours = {
    hours: {
      "1": [{ alias: "Monday", timing: [[540, 1020]] }],    // 9 AM - 5 PM
      "2": [{ alias: "Tuesday", timing: [[540, 1020]] }],   // 9 AM - 5 PM
      "3": [{ alias: "Wednesday", timing: [[540, 1020]] }], // 9 AM - 5 PM
      "4": [{ alias: "Thursday", timing: [[540, 1020]] }],  // 9 AM - 5 PM
      "5": [{ alias: "Friday", timing: [[540, 1020]] }],    // 9 AM - 5 PM
      "6": [{ alias: "Saturday", timing: [[0, 0]] }],       // Closed
      "7": [{ alias: "Sunday", timing: [[0, 0]] }]          // Closed
    }
  };

  // Test with local time
  console.log("\n👤 LOCAL TIME VALIDATION:");
  testSpecificOffset("+00:00", sampleHours.hours); // No offset = local time

  // Test with business offset
  console.log(`\n🏢 BUSINESS OFFSET VALIDATION (${offset}):`);
  testSpecificOffset(offset, sampleHours.hours);

  return {
    sampleHours,
    offset,
    message: "Check console logs above for comparison"
  };
}

// Make functions available globally
if (typeof window !== "undefined") {
  window.testTimezoneOffsetValidation = testTimezoneOffsetValidation;
  window.testSpecificOffset = testSpecificOffset;
  window.compareOffsetValidation = compareOffsetValidation;
  
  console.log("🔧 Timezone offset validation test functions loaded!");
  console.log("Available functions:");
  console.log("- window.testTimezoneOffsetValidation()");
  console.log("- window.testSpecificOffset('-04:00')");
  console.log("- window.compareOffsetValidation('-07:00')");
}

// Auto-run basic test
testTimezoneOffsetValidation();
