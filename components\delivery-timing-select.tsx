"use client";

import { useState, useRef, useEffect } from "react";
import ClockIcon from "./icons/clock-icon";
import ScheduleIcon from "./icons/schedule-icon";
import SubscriptionIcon from "./icons/subscription-icon";
import { useBusiness } from "@/context/business-context";
import { useToast } from "@/context/toast-context";
import { validateBusinessHoursWithMessage } from "@/services/business-api";

const STORAGE_KEY = "ezeats-delivery-timing";

type DeliveryTimingSelectProps = {
  value: string;
  onChange: (value: string) => void;
  orderType: "delivery" | "pickup";
  className?: string;
};

export default function DeliveryTimingSelect({
  value: propValue,
  onChange,
  orderType,
  className = "",
}: DeliveryTimingSelectProps) {
  // Initialize state with localStorage value or prop value
  const [value, setValue] = useState(() => {
    // Check if we're in the browser
    if (typeof window !== "undefined") {
      const savedValue = localStorage.getItem(STORAGE_KEY);
      return savedValue || propValue;
    }
    return propValue;
  });

  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);

  // Business context and toast
  const { selectedBranch } = useBusiness();
  const { showToast } = useToast();

  // Check business hours for "now" option
  const businessValidation = validateBusinessHoursWithMessage(
    selectedBranch,
    "now"
  );
  const isBusinessOpen = businessValidation.isValid;

  // Sync with prop value when it changes
  useEffect(() => {
    if (propValue !== value) {
      setValue(propValue);
    }
  }, [propValue]);

  // Auto-switch from "now" to "later" if business becomes closed
  useEffect(() => {
    if (value === "now" && !isBusinessOpen) {
      console.log(
        "🔄 DeliveryTiming - Business closed, auto-switching from 'now' to 'later'"
      );
      // We need to check options after they're defined, so we'll do this in a timeout
      setTimeout(() => {
        setValue("later");
        localStorage.setItem(STORAGE_KEY, "later");
        onChange("later");
      }, 100);
    }
  }, [value, isBusinessOpen, onChange]);

  // Options with icons and disabled state
  const options = [
    {
      value: "now",
      label: orderType === "delivery" ? "Deliver now" : "Pickup now",
      icon: <ClockIcon size={18} className="mr-2" />,
      disabled: !isBusinessOpen,
      disabledReason: businessValidation.message,
      statusText: !isBusinessOpen
        ? businessValidation.todayHours
          ? `Currently closed - Today: ${businessValidation.todayHours}`
          : "Currently closed"
        : businessValidation.status === "open_24_7"
        ? "Open 24/7"
        : businessValidation.todayHours
        ? `Open now - Today: ${businessValidation.todayHours}`
        : "Open now",
    },
    {
      value: "later",
      label: "Schedule for later",
      icon: <ScheduleIcon size={18} className="mr-2" />,
      disabled: false,
    },
    {
      value: "weekly",
      label: "Weekly subscription",
      icon: <SubscriptionIcon size={18} className="mr-2" />,
      disabled: false,
    },
    {
      value: "biweekly",
      label: "Bi-weekly subscription",
      icon: <SubscriptionIcon size={18} className="mr-2" />,
      disabled: false,
    },
  ];

  // Get the selected option
  const selectedOption =
    options.find((option) => option.value === value) || options[0];

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Notify parent on initial load if we loaded from localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedValue = localStorage.getItem(STORAGE_KEY);
      if (savedValue && savedValue !== propValue) {
        onChange(savedValue);
      }
    }
  }, []);

  // Function to handle option selection
  const handleOptionSelect = (optionValue: string, option: any) => {
    // Check if option is disabled
    if (option.disabled) {
      console.log("🚫 DeliveryTiming - Disabled option selected:", optionValue);
      showToast({
        message:
          option.disabledReason || "This option is currently unavailable",
        type: "warning",
        duration: 6000,
      });
      setIsOpen(false);
      return;
    }

    // Only update if value is changing
    if (optionValue !== value) {
      console.log("✅ DeliveryTiming - Option selected:", optionValue);

      // Save to localStorage
      localStorage.setItem(STORAGE_KEY, optionValue);

      // Update internal state
      setValue(optionValue);

      // Dispatch a custom event to notify that the user changed the delivery timing
      const event = new CustomEvent("userChangedDeliveryTiming");
      document.dispatchEvent(event);

      // Call the onChange handler
      onChange(optionValue);
    }

    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      <div className="relative">
        <button
          type="button"
          className={`w-full h-[62px] py-2 pr-2 pl-[13px] border border-gray-300 rounded-md bg-white font-poppins text-left flex items-center ${
            selectedOption.disabled ? "text-gray-400" : "text-black"
          }`}
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <div className={selectedOption.disabled ? "opacity-50" : ""}>
              {selectedOption.icon}
            </div>
            <span className={selectedOption.disabled ? "opacity-50" : ""}>
              {selectedOption.label}
              {selectedOption.value === "now" && selectedOption.statusText && (
                <span
                  className={`text-xs block ${
                    selectedOption.disabled ? "text-red-500" : "text-green-600"
                  }`}
                >
                  {selectedOption.statusText}
                </span>
              )}
            </span>
          </div>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className={`h-5 w-5 text-gray-400 transition-transform ${
                isOpen ? "transform rotate-180" : ""
              }`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </button>
      </div>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              className={`w-full text-left px-4 py-2 flex items-center ${
                option.disabled
                  ? "cursor-not-allowed opacity-50 bg-gray-100 text-gray-400"
                  : "hover:bg-gray-100 cursor-pointer"
              } ${
                option.value === value && !option.disabled ? "bg-gray-50" : ""
              }`}
              onClick={() => handleOptionSelect(option.value, option)}
              disabled={option.disabled}
            >
              <div className={option.disabled ? "opacity-50" : ""}>
                {option.icon}
              </div>
              <span className={option.disabled ? "opacity-50" : ""}>
                {option.label}
                {option.value === "now" && option.statusText && (
                  <span
                    className={`text-xs block mt-1 ${
                      option.disabled ? "text-red-500" : "text-green-600"
                    }`}
                  >
                    {option.statusText}
                  </span>
                )}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
