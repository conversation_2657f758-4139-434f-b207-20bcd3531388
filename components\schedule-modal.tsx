"use client";

import { useState, useEffect } from "react";
import { X, Loader2 } from "lucide-react";
import { useCart } from "@/context/cart-context";
import { useRouter } from "next/navigation";
import { useBusiness } from "@/context/business-context";
import { useToast } from "@/context/toast-context";
import { trackMealSubscription } from "@/utils/analytics";

type ScheduleModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function ScheduleModal({ isOpen, onClose }: ScheduleModalProps) {
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [availableDates, setAvailableDates] = useState<
    { value: string; label: string }[]
  >([]);
  const [timeSlots, setTimeSlots] = useState<string[]>([]);
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { setScheduledDateTime, setOrderSchedule, deliveryTiming, totalPrice } =
    useCart();
  const { selectedBranch } = useBusiness();
  const { showToast } = useToast();
  const router = useRouter();
  const branchId = selectedBranch?.id || "18784"; // Use selected branch ID or default;

  // Generate dates for the next 7 days
  useEffect(() => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      // Format the date as YYYY-MM-DD for the value
      const value = date.toISOString().split("T")[0];

      // Format the label as YYYY-MM-DD as requested
      const label = value; // Use the ISO format directly as the label

      dates.push({ value, label });
    }

    setAvailableDates(dates);
    // Set default to today
    setSelectedDate(dates[0].value);
  }, []);

  // Fetch time slots when date changes
  useEffect(() => {
    if (!selectedDate) return;

    const fetchTimeSlots = async () => {
      setIsLoadingTimeSlots(true);
      try {
        const response = await fetch(
          `https://tossdown.com/api/pickup_hours_time_slots?bid=${branchId}&date=${selectedDate}&type=pickup`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch time slots");
        }

        const data = await response.json();

        if (data.status === "200" && Array.isArray(data.slots)) {
          setTimeSlots(data.slots);
          // Set default time slot if available
          if (data.slots.length > 0) {
            setSelectedTime(data.slots[0]);
          } else {
            setSelectedTime("");
          }
        } else {
          setTimeSlots([]);
          setSelectedTime("");
        }
      } catch (error) {
        console.error("Error fetching time slots:", error);
        setTimeSlots([]);
        setSelectedTime("");
      } finally {
        setIsLoadingTimeSlots(false);
      }
    };

    fetchTimeSlots();
  }, [selectedDate, branchId]);

  const handleSchedule = () => {
    if (selectedDate && selectedTime) {
      setIsSubmitting(true);

      try {
        // Create the schedule data object
        const scheduleData = {
          date: selectedDate,
          time: selectedTime,
        };

        // Set in context (this will trigger the useEffect to save to localStorage)
        setOrderSchedule(scheduleData);

        // Set the combined date and time string for backward compatibility
        const scheduledDateTime = `${selectedDate} ${selectedTime}`;
        setScheduledDateTime(scheduledDateTime);

        // Track subscription event for Meta Pixel if this is a weekly or bi-weekly subscription
        if (deliveryTiming === "weekly" || deliveryTiming === "biweekly") {
          // Track the meal subscription event
          trackMealSubscription(
            deliveryTiming as "weekly" | "biweekly",
            scheduleData,
            totalPrice // Use the current cart total as the estimated value
          );
        }

        // Close the modal
        onClose();

        // Navigate to menu page after scheduling
        router.push("/menu");
      } catch (error) {
        console.error("Error setting schedule:", error);
        showToast({
          message: "Failed to set schedule. Please try again.",
          type: "error",
          duration: 4000,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[12000] flex items-start justify-center p-4"
      onClick={onClose} // Close when clicking the overlay
    >
      <div
        className="bg-white rounded-lg w-full max-w-md schedule_modal_parent"
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside from closing
      >
        {/* Header with close button */}
        <div className="flex justify-between items-center schedule_modal_data">
          <h2 className="text-2xl font-black">PLEASE PICK A DATE & TIME</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-black"
            type="button"
            aria-label="Close modal"
          >
            <X size={24} />
          </button>
        </div>

        <div className="schedule_modal_field">
          <select
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md bg-white text-black mb-4"
          >
            {availableDates.map((date) => (
              <option key={date.value} value={date.value}>
                {date.label}
              </option>
            ))}
          </select>

          {isLoadingTimeSlots ? (
            <div className="flex justify-center p-3 border border-gray-300 rounded-md">
              <Loader2 className="animate-spin h-5 w-5 text-gray-500" />
            </div>
          ) : (
            <select
              value={selectedTime}
              onChange={(e) => setSelectedTime(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md bg-white text-black"
              disabled={timeSlots.length === 0}
            >
              {timeSlots.length > 0 ? (
                timeSlots.map((time) => (
                  <option key={time} value={time}>
                    {time}
                  </option>
                ))
              ) : (
                <option value="">No time slots available</option>
              )}
            </select>
          )}
        </div>

        <button
          onClick={handleSchedule}
          className="w-full bg-black text-white py-3 text-xl font-bold disabled:bg-gray-300 disabled:cursor-not-allowed schedule_modal_button"
          disabled={
            !selectedDate || !selectedTime || isLoadingTimeSlots || isSubmitting
          }
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center">
              <Loader2 className="animate-spin h-5 w-5 mr-2" />
              <span>SCHEDULING...</span>
            </div>
          ) : (
            "SCHEDULE"
          )}
        </button>
      </div>
    </div>
  );
}
