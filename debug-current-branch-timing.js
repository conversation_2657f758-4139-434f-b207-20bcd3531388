/**
 * Debug script to check the current branch timing data
 * 
 * Run this in the browser console to see what timing data
 * your current branch has and verify the multiple timing slots functionality.
 */

function debugCurrentBranchTiming() {
  console.log("🔍 DEBUG: Checking current branch timing data");
  console.log("=" .repeat(60));

  // Try to access the business context from the window
  // This might not work directly, so we'll also provide manual testing
  
  // Check if we can access React DevTools or global state
  if (typeof window !== "undefined") {
    console.log("🌐 Window object available");
    
    // Try to find business data in common locations
    const possiblePaths = [
      'window.__NEXT_DATA__',
      'window.businessData',
      'window.selectedBranch'
    ];
    
    possiblePaths.forEach(path => {
      try {
        const value = eval(path);
        if (value) {
          console.log(`✅ Found data at ${path}:`, value);
        }
      } catch (e) {
        console.log(`❌ No data at ${path}`);
      }
    });
  }

  // Instructions for manual testing
  console.log("\n📋 MANUAL TESTING INSTRUCTIONS:");
  console.log("1. Open the cart page");
  console.log("2. Try to place an order (click 'Place Order' button)");
  console.log("3. Look for these console logs:");
  console.log("   🛒 Cart - Checking business hours...");
  console.log("   🕐 validateBusinessHours - Current time...");
  console.log("   📋 validateBusinessHours - Found X timing slot(s)...");
  console.log("   🔍 validateBusinessHours - Checking slot...");
  console.log("   ⏰ validateBusinessHours - Range...");

  console.log("\n🧪 TESTING WITH SAMPLE DATA:");
  console.log("Run: window.debugMultipleTimingSlots() to test with sample data");
  
  console.log("\n🔧 FORCE TEST CURRENT VALIDATION:");
  console.log("If you want to test the current validation logic manually:");
  console.log("1. Copy your branch timing JSON");
  console.log("2. Run: testTimingValidation(timingJSON)");
  
  return {
    instructions: "Follow the manual testing steps above",
    nextSteps: [
      "Check cart page console logs",
      "Run window.debugMultipleTimingSlots()",
      "Verify timing data format"
    ]
  };
}

// Function to test timing validation with custom data
function testTimingValidation(timingJSON) {
  console.log("🧪 TESTING: Custom timing validation");
  console.log("Input timing data:", timingJSON);
  
  try {
    const timingData = typeof timingJSON === 'string' ? JSON.parse(timingJSON) : timingJSON;
    
    const now = new Date();
    const currentDay = now.getDay() === 0 ? 7 : now.getDay();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    
    console.log(`Current: Day ${currentDay}, ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')} (${currentMinutes} minutes)`);
    
    const dayHours = timingData.hours?.[currentDay.toString()];
    
    if (!dayHours || dayHours.length === 0) {
      console.log("❌ No timing data for current day");
      return { isOpen: false, reason: "No timing data" };
    }
    
    console.log(`📋 Found ${dayHours.length} timing slot(s) for today`);
    
    let isOpen = false;
    const allRanges = [];
    
    for (let slotIndex = 0; slotIndex < dayHours.length; slotIndex++) {
      const slot = dayHours[slotIndex];
      console.log(`🔍 Slot ${slotIndex + 1}: ${slot.alias || 'Unnamed'}`);
      
      if (!slot.timing || slot.timing.length === 0) {
        console.log("✅ 24/7 operation");
        return { isOpen: true, reason: "24/7" };
      }
      
      for (let rangeIndex = 0; rangeIndex < slot.timing.length; rangeIndex++) {
        const [start, end] = slot.timing[rangeIndex];
        
        // Convert minutes to time string
        const startTime = `${Math.floor(start/60)}:${(start%60).toString().padStart(2,'0')}`;
        const endTime = `${Math.floor(end/60)}:${(end%60).toString().padStart(2,'0')}`;
        const rangeStr = `${startTime} - ${endTime}`;
        allRanges.push(rangeStr);
        
        console.log(`⏰ Range ${rangeIndex + 1}: ${rangeStr}`);
        
        if (start === end) {
          console.log("❌ Closed period");
          continue;
        }
        
        if (currentMinutes >= start && currentMinutes <= end) {
          console.log("✅ Currently OPEN in this range");
          isOpen = true;
        } else {
          console.log("❌ Outside this range");
        }
      }
    }
    
    const result = {
      isOpen,
      reason: isOpen ? "Open in one of the ranges" : "Outside all ranges",
      allRanges,
      displayHours: allRanges.join(", ")
    };
    
    console.log("🔧 Final result:", result);
    return result;
    
  } catch (error) {
    console.error("💥 Error testing timing validation:", error);
    return { error: error.message };
  }
}

// Make functions available globally
if (typeof window !== "undefined") {
  window.debugCurrentBranchTiming = debugCurrentBranchTiming;
  window.testTimingValidation = testTimingValidation;
  
  console.log("🔧 Debug functions loaded!");
  console.log("Run: window.debugCurrentBranchTiming()");
}

// Auto-run the debug function
debugCurrentBranchTiming();
