/**
 * Test script for timezone-aware business hours validation
 * 
 * This script tests the business hours system with different timezones
 * to ensure validation uses business timezone instead of user's local time.
 * 
 * Run this in the browser console to test the functionality.
 */

function testTimezoneValidation() {
  console.log("🌍 TESTING: Timezone-aware Business Hours Validation");
  console.log("=" .repeat(60));

  // Test different timezones
  const testTimezones = [
    "America/New_York",    // EST/EDT
    "America/Los_Angeles", // PST/PDT
    "America/Chicago",     // CST/CDT
    "America/Denver",      // MST/MDT
    "Europe/London",       // GMT/BST
    "Asia/Tokyo",          // JST
    "Australia/Sydney",    // AEST/AEDT
  ];

  console.log("\n📋 TESTING DIFFERENT TIMEZONES:");
  
  testTimezones.forEach(timezone => {
    console.log(`\n🌍 Testing timezone: ${timezone}`);
    
    try {
      // Get current time in this timezone
      const now = new Date();
      const timeInTZ = now.toLocaleString("en-US", { timeZone: timezone });
      const dateInTZ = new Date(timeInTZ);
      
      console.log(`   User local time: ${now.toLocaleTimeString()}`);
      console.log(`   Business time (${timezone}): ${dateInTZ.toLocaleTimeString()}`);
      
      // Test with debug function if available
      if (typeof window !== "undefined" && window.debugTiming) {
        const result = window.debugTiming(timezone);
        console.log(`   Debug result:`, result);
      }
      
    } catch (error) {
      console.error(`   ❌ Error testing ${timezone}:`, error.message);
    }
  });

  console.log("\n🧪 TESTING BUSINESS HOURS WITH TIMEZONE:");
  console.log("1. Check console logs for timezone comparison");
  console.log("2. Look for 'User local time' vs 'Business time' logs");
  console.log("3. Verify business hours validation uses business timezone");

  return {
    testTimezones,
    instructions: "Check console logs for timezone validation details"
  };
}

// Function to test specific timezone scenario
function testSpecificTimezone(timezone, businessHours) {
  console.log(`🌍 TESTING SPECIFIC TIMEZONE: ${timezone}`);
  console.log("=" .repeat(50));

  try {
    // Get current times
    const userNow = new Date();
    const businessTimeStr = userNow.toLocaleString("en-US", { timeZone: timezone });
    const businessTime = new Date(businessTimeStr);
    
    console.log(`👤 User local time: ${userNow.toLocaleTimeString()} (${userNow.getTimezoneOffset() / -60} UTC offset)`);
    console.log(`🏢 Business time (${timezone}): ${businessTime.toLocaleTimeString()}`);
    
    // Calculate difference
    const timeDiff = (businessTime.getTime() - userNow.getTime()) / (1000 * 60 * 60);
    console.log(`⏰ Time difference: ${timeDiff.toFixed(1)} hours`);
    
    // Test business hours if provided
    if (businessHours) {
      const businessMinutes = businessTime.getHours() * 60 + businessTime.getMinutes();
      const dayOfWeek = businessTime.getDay() === 0 ? 7 : businessTime.getDay();
      
      console.log(`📅 Business day: ${dayOfWeek} (${['Sun','Mon','Tue','Wed','Thu','Fri','Sat'][businessTime.getDay()]})`);
      console.log(`🕐 Business minutes: ${businessMinutes}`);
      
      // Check if business should be open
      const dayHours = businessHours[dayOfWeek.toString()];
      if (dayHours && dayHours.length > 0) {
        let isOpen = false;
        for (const slot of dayHours) {
          if (!slot.timing || slot.timing.length === 0) {
            isOpen = true;
            console.log(`✅ Business is open 24/7`);
            break;
          }
          
          for (const [start, end] of slot.timing) {
            if (start === end) continue;
            if (businessMinutes >= start && businessMinutes <= end) {
              isOpen = true;
              console.log(`✅ Business is OPEN (${Math.floor(start/60)}:${(start%60).toString().padStart(2,'0')} - ${Math.floor(end/60)}:${(end%60).toString().padStart(2,'0')})`);
              break;
            }
          }
        }
        
        if (!isOpen) {
          console.log(`❌ Business is CLOSED`);
          dayHours.forEach((slot, i) => {
            if (slot.timing && slot.timing.length > 0) {
              slot.timing.forEach(([start, end]) => {
                if (start !== end) {
                  console.log(`   Hours: ${Math.floor(start/60)}:${(start%60).toString().padStart(2,'0')} - ${Math.floor(end/60)}:${(end%60).toString().padStart(2,'0')}`);
                }
              });
            }
          });
        }
      } else {
        console.log(`❌ No business hours for this day`);
      }
    }
    
    return {
      userTime: userNow,
      businessTime: businessTime,
      timezone: timezone,
      timeDifference: timeDiff
    };
    
  } catch (error) {
    console.error(`💥 Error testing timezone ${timezone}:`, error);
    return { error: error.message };
  }
}

// Function to compare validation results between local and business timezone
function compareTimezoneValidation(timezone) {
  console.log(`🔍 COMPARING VALIDATION: Local vs ${timezone}`);
  console.log("=" .repeat(50));

  // Sample business hours (9 AM - 5 PM)
  const sampleHours = {
    hours: {
      "1": [{ alias: "Monday", timing: [[540, 1020]] }],    // 9 AM - 5 PM
      "2": [{ alias: "Tuesday", timing: [[540, 1020]] }],   // 9 AM - 5 PM
      "3": [{ alias: "Wednesday", timing: [[540, 1020]] }], // 9 AM - 5 PM
      "4": [{ alias: "Thursday", timing: [[540, 1020]] }],  // 9 AM - 5 PM
      "5": [{ alias: "Friday", timing: [[540, 1020]] }],    // 9 AM - 5 PM
      "6": [{ alias: "Saturday", timing: [[0, 0]] }],       // Closed
      "7": [{ alias: "Sunday", timing: [[0, 0]] }]          // Closed
    }
  };

  // Test with local time
  console.log("\n👤 LOCAL TIME VALIDATION:");
  testSpecificTimezone(null, sampleHours.hours);

  // Test with business timezone
  console.log(`\n🏢 BUSINESS TIMEZONE VALIDATION (${timezone}):`);
  testSpecificTimezone(timezone, sampleHours.hours);

  return {
    sampleHours,
    timezone,
    message: "Check console logs above for comparison"
  };
}

// Make functions available globally
if (typeof window !== "undefined") {
  window.testTimezoneValidation = testTimezoneValidation;
  window.testSpecificTimezone = testSpecificTimezone;
  window.compareTimezoneValidation = compareTimezoneValidation;
  
  console.log("🔧 Timezone validation test functions loaded!");
  console.log("Available functions:");
  console.log("- window.testTimezoneValidation()");
  console.log("- window.testSpecificTimezone('America/New_York')");
  console.log("- window.compareTimezoneValidation('America/Los_Angeles')");
}

// Auto-run basic test
testTimezoneValidation();
