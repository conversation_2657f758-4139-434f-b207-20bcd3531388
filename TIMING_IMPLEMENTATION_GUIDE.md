# Branch Timing Implementation Guide

## Overview

This implementation allows you to control user access based on business hours using the 'hours' key from the timing JSON structure.

## Key Features

### 1. Automatic Timing Parsing

The system automatically parses timing data from the branch's `timing` field when business locations are fetched.

### 2. Real-time Status Checking

- Checks current time against business hours
- Uses the 'hours' key from timing data
- Handles multiple time slots per day

### 3. User-Friendly Restrictions

- Prevents orders when business is closed
- Shows helpful messages with current hours
- Allows scheduling for later when business is closed

## Usage in Components

### Basic Status Check

```tsx
import { useBusiness } from "@/context/business-context";

function MyComponent() {
  const { isBranchOpen, getBranchStatus } = useBusiness();

  const isOpen = isBranchOpen();
  const status = getBranchStatus();

  return (
    <div>
      <p>Status: {isOpen ? "Open" : "Closed"}</p>
      <p>Today's Hours: {status.todayHours}</p>
    </div>
  );
}
```

### Using the Business Hours Component

```tsx
import BusinessHoursStatus from "@/components/business-hours-status";

function Header() {
  return (
    <div>
      <BusinessHoursStatus showDetails={true} />
    </div>
  );
}
```

### Order Validation Hook

```tsx
import { useCanOrder } from "@/components/business-hours-status";

function OrderButton() {
  const { canOrder, reason } = useCanOrder();

  return (
    <button disabled={!canOrder}>{canOrder ? "Place Order" : reason}</button>
  );
}
```

## Timing Data Format

The system expects timing data in this format:

```json
{
  "hours": {
    "1": [{ "alias": "Monday", "timing": [[600, 1200]] }],
    "2": [{ "alias": "Tuesday", "timing": [[600, 1200]] }],
    "6": [{ "alias": "Saturday", "timing": [] }], // 24/7
    "7": [{ "alias": "Sunday", "timing": [[0, 0]] }] // Closed
  }
}
```

Note: Only the 'hours' key is used for timing validation.

### Timing Rules

- **Empty array `[]`**: Open 24/7
- **Same values `[0,0]`**: Closed
- **Different values `[600,1200]`**: Open from 10:00 AM to 8:00 PM
- **Multiple slots**: Supports split hours (e.g., lunch break)

## Integration Points

### 1. Cart Validation

The cart automatically checks business hours before allowing checkout for immediate orders.

### 2. Delivery Timing Selection

The system respects business hours when showing "Order Now" vs "Schedule Later" options.

### 3. Visual Indicators

Business hours status is shown in the cart and can be added to other components.

## Customization

### Adding Custom Validation

```tsx
// In your component
const { isBranchOpen } = useBusiness();
const isOpen = isBranchOpen();

if (!isOpen && orderTiming === "now") {
  // Show scheduling modal or disable ordering
}
```

### Custom Status Messages

```tsx
const { getBranchStatus } = useBusiness();
const status = getBranchStatus();
const message =
  status.status === "open_24_7"
    ? "We're always open!"
    : status.isOpen
    ? `Open until ${status.todayHours?.split(" - ")[1]}`
    : `Closed - Opens ${status.nextOpenTime}`;
```

## Testing

The implementation has been tested with the provided JSON structure and handles all edge cases:

- 24/7 operations (Saturday in example)
- Closed days (Sunday in example)
- Split hours (Friday in example)
- Regular hours (Monday-Thursday in example)

## Future Enhancements

1. **Time Zone Support**: Currently uses local time, can be extended for branch-specific time zones
2. **Holiday Hours**: Can be extended to handle special holiday schedules
3. **Advance Ordering**: Can be enhanced to show available time slots for future orders
4. **Notification System**: Can notify users when business reopens
