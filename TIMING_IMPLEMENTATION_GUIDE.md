# Branch Timing Implementation Guide

## Overview

This implementation allows you to control user access based on business hours using the 'hours' key from the timing JSON structure. The system now supports **timezone-aware validation** to ensure accurate business hours checking regardless of the user's location.

## Key Features

### 1. Automatic Timing Parsing

The system automatically parses timing data from the branch's `timing` field when business locations are fetched.

### 2. Timezone-Aware Validation

- **Uses business timezone**: Validation is performed using the business's `time_zone` field
- **Accurate across regions**: Works correctly for users in different timezones than the business
- **Automatic conversion**: Converts user's local time to business timezone for validation

### 3. Real-time Status Checking

- Checks current time against business hours in business timezone
- Uses the 'hours' key from timing data
- Handles multiple time slots per day

### 4. User-Friendly Restrictions

- Prevents orders when business is closed
- Shows helpful messages with current hours
- Allows scheduling for later when business is closed

## Usage in Components

### Basic Status Check

```tsx
import { useBusiness } from "@/context/business-context";

function MyComponent() {
  const { isBranchOpen, getBranchStatus } = useBusiness();

  const isOpen = isBranchOpen();
  const status = getBranchStatus();

  return (
    <div>
      <p>Status: {isOpen ? "Open" : "Closed"}</p>
      <p>Today's Hours: {status.todayHours}</p>
    </div>
  );
}
```

### Using the Business Hours Component

```tsx
import BusinessHoursStatus from "@/components/business-hours-status";

function Header() {
  return (
    <div>
      <BusinessHoursStatus showDetails={true} />
    </div>
  );
}
```

### Order Validation Hook

```tsx
import { useCanOrder } from "@/components/business-hours-status";

function OrderButton() {
  const { canOrder, reason } = useCanOrder();

  return (
    <button disabled={!canOrder}>{canOrder ? "Place Order" : reason}</button>
  );
}
```

## Timing Data Format

The system expects timing data in this format:

```json
{
  "hours": {
    "1": [{ "alias": "Monday", "timing": [[600, 1200]] }],
    "2": [{ "alias": "Tuesday", "timing": [[600, 1200]] }],
    "3": [
      {
        "alias": "Wednesday",
        "timing": [
          [600, 780],
          [840, 1200]
        ]
      }
    ], // Lunch break
    "4": [
      {
        "alias": "Thursday",
        "timing": [
          [540, 720],
          [780, 1080]
        ]
      }
    ], // Multiple shifts
    "5": [{ "alias": "Friday", "timing": [[600, 1440]] }], // Extended hours
    "6": [{ "alias": "Saturday", "timing": [] }], // 24/7
    "7": [{ "alias": "Sunday", "timing": [[0, 0]] }] // Closed
  }
}
```

Note: Only the 'hours' key is used for timing validation.

### Timing Rules

- **Empty array `[]`**: Open 24/7
- **Same values `[0,0]`**: Closed
- **Different values `[600,1200]`**: Open from 10:00 AM to 8:00 PM
- **Multiple slots**: Supports split hours (e.g., lunch break)

### Multiple Timing Slots Examples

#### Lunch Break Example (Wednesday)

```json
"3": [{ "alias": "Wednesday", "timing": [[600, 780], [840, 1200]] }]
```

- **First slot**: 10:00 AM - 1:00 PM (600-780 minutes)
- **Break**: 1:00 PM - 2:00 PM (closed for lunch)
- **Second slot**: 2:00 PM - 8:00 PM (840-1200 minutes)
- **Display**: "10:00 AM - 1:00 PM, 2:00 PM - 8:00 PM"

#### Split Shift Example (Thursday)

```json
"4": [{ "alias": "Thursday", "timing": [[540, 720], [780, 1080]] }]
```

- **Morning shift**: 9:00 AM - 12:00 PM (540-720 minutes)
- **Break**: 12:00 PM - 1:00 PM (closed)
- **Afternoon shift**: 1:00 PM - 6:00 PM (780-1080 minutes)
- **Display**: "9:00 AM - 12:00 PM, 1:00 PM - 6:00 PM"

#### Extended Hours Example (Friday)

```json
"5": [{ "alias": "Friday", "timing": [[600, 1440]] }]
```

- **All day**: 10:00 AM - 12:00 AM (600-1440 minutes)
- **Display**: "10:00 AM - 12:00 AM"

## Integration Points

### 1. Cart Validation

The cart automatically checks business hours before allowing checkout for immediate orders.

### 2. Delivery Timing Selection

The system respects business hours when showing "Order Now" vs "Schedule Later" options.

### 3. Visual Indicators

Business hours status is shown in the cart and can be added to other components.

## Customization

### Adding Custom Validation

```tsx
// In your component
const { isBranchOpen } = useBusiness();
const isOpen = isBranchOpen();

if (!isOpen && orderTiming === "now") {
  // Show scheduling modal or disable ordering
}
```

### Custom Status Messages

```tsx
const { getBranchStatus } = useBusiness();
const status = getBranchStatus();
const message =
  status.status === "open_24_7"
    ? "We're always open!"
    : status.isOpen
    ? `Open until ${status.todayHours?.split(" - ")[1]}`
    : `Closed - Opens ${status.nextOpenTime}`;
```

## Testing

The implementation has been tested with the provided JSON structure and handles all edge cases:

- **Regular hours**: Monday-Tuesday (single time slot per day)
- **Multiple timing slots**: Wednesday-Thursday (lunch breaks and split shifts)
- **Extended hours**: Friday (long single slot)
- **24/7 operations**: Saturday (empty timing array)
- **Closed days**: Sunday (same start/end time)

### Testing Multiple Timing Slots

To test the multiple timing slots functionality:

1. **During business hours**: The system should show "Open Now" and display all time ranges
2. **During break periods**: The system should show "Closed" with next opening time
3. **Outside all hours**: The system should show "Closed" with today's complete schedule

### Console Debugging

The enhanced logging provides detailed information about:

- Number of timing slots found for the current day
- Each time range being checked
- Whether current time falls within any range
- Multiple slots detection and formatting

### Debug Functions

Two debug functions are available in the browser console for testing:

#### Basic Timing Debug

```javascript
// Test current timing status
window.debugTiming();
```

#### Multiple Timing Slots Debug

```javascript
// Test with default example data (includes lunch breaks)
window.debugMultipleTimingSlots();

// Test with custom timing data
window.debugMultipleTimingSlots({
  hours: {
    1: [
      {
        alias: "Monday",
        timing: [
          [600, 780],
          [840, 1200],
        ],
      },
    ],
  },
});
```

#### Timezone Testing

```javascript
// Test timezone validation
window.testTimezoneValidation();

// Test specific timezone
window.testSpecificTimezone("America/Los_Angeles");

// Compare local vs business timezone validation
window.compareTimezoneValidation("Europe/London");

// Test with specific timezone in debug functions
window.debugTiming("America/New_York");
window.debugMultipleTimingSlots(null, "America/Chicago");
```

These functions will log detailed information about:

- Current day and time (both user local and business timezone)
- All timing slots for the day
- Whether business is currently open
- Formatted display hours
- Timezone differences and conversions

## Future Enhancements

1. **Time Zone Support**: ✅ **IMPLEMENTED** - Now uses business timezone for accurate validation
2. **Holiday Hours**: Can be extended to handle special holiday schedules
3. **Advance Ordering**: Can be enhanced to show available time slots for future orders
4. **Notification System**: Can notify users when business reopens
