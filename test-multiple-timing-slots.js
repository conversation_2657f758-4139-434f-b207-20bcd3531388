/**
 * Test script for multiple timing slots functionality
 * 
 * This script tests the business hours system with multiple timing slots per day,
 * including lunch breaks and split shifts.
 * 
 * Run this in the browser console to test the functionality.
 */

// Test data with multiple timing slots
const testTimingData = {
  hours: {
    // Monday: Regular hours (single slot)
    "1": [{ alias: "Monday", timing: [[600, 1200]] }], // 10 AM - 8 PM
    
    // Tuesday: Lunch break (two slots)
    "2": [{ 
      alias: "Tuesday", 
      timing: [[600, 780], [840, 1200]] // 10 AM - 1 PM, 2 PM - 8 PM
    }],
    
    // Wednesday: Split shift (two slots)
    "3": [{ 
      alias: "Wednesday", 
      timing: [[540, 720], [780, 1080]] // 9 AM - 12 PM, 1 PM - 6 PM
    }],
    
    // Thursday: Extended hours (single slot)
    "4": [{ alias: "Thursday", timing: [[600, 1440]] }], // 10 AM - 12 AM
    
    // Friday: 24/7 (empty timing)
    "5": [{ alias: "Friday", timing: [] }],
    
    // Saturday: Closed (same start/end)
    "6": [{ alias: "Saturday", timing: [[0, 0]] }],
    
    // Sunday: Multiple slots with gap
    "7": [{ 
      alias: "Sunday", 
      timing: [[600, 780], [900, 1200]] // 10 AM - 1 PM, 3 PM - 8 PM
    }]
  }
};

// Test scenarios for different times
const testScenarios = [
  { day: 2, time: 720, description: "Tuesday 12:00 PM (during first slot)" },
  { day: 2, time: 810, description: "Tuesday 1:30 PM (during lunch break)" },
  { day: 2, time: 900, description: "Tuesday 3:00 PM (during second slot)" },
  { day: 3, time: 600, description: "Wednesday 10:00 AM (during morning shift)" },
  { day: 3, time: 750, description: "Wednesday 12:30 PM (between shifts)" },
  { day: 3, time: 840, description: "Wednesday 2:00 PM (during afternoon shift)" },
  { day: 5, time: 120, description: "Friday 2:00 AM (24/7 operation)" },
  { day: 6, time: 720, description: "Saturday 12:00 PM (closed day)" },
  { day: 7, time: 840, description: "Sunday 2:00 PM (between Sunday slots)" }
];

/**
 * Convert minutes to 12-hour format time string
 */
function minutesToTime12Hour(minutes) {
  const hours24 = Math.floor(minutes / 60);
  const mins = minutes % 60;
  const hours12 = hours24 === 0 ? 12 : hours24 > 12 ? hours24 - 12 : hours24;
  const ampm = hours24 >= 12 ? "PM" : "AM";
  return `${hours12}:${mins.toString().padStart(2, "0")} ${ampm}`;
}

/**
 * Test business hours validation for a specific day and time
 */
function testBusinessHours(day, timeInMinutes, timingData) {
  const dayHours = timingData.hours?.[day.toString()];
  
  if (!dayHours || dayHours.length === 0) {
    return { isOpen: false, reason: "No timing data for this day" };
  }

  // Check if any timing slot is 24/7
  for (const timingSlot of dayHours) {
    if (!timingSlot.timing || timingSlot.timing.length === 0) {
      return { isOpen: true, reason: "24/7 operation" };
    }
  }

  // Check all time ranges
  for (const slot of dayHours) {
    if (slot.timing && slot.timing.length > 0) {
      for (const timeRange of slot.timing) {
        if (timeRange.length === 2) {
          const [openTime, closeTime] = timeRange;
          
          if (openTime === closeTime) {
            continue; // Skip closed periods
          }
          
          if (timeInMinutes >= openTime && timeInMinutes <= closeTime) {
            return { 
              isOpen: true, 
              reason: `Open in slot: ${minutesToTime12Hour(openTime)} - ${minutesToTime12Hour(closeTime)}` 
            };
          }
        }
      }
    }
  }

  return { isOpen: false, reason: "Outside all business hours" };
}

/**
 * Get formatted hours display for a day
 */
function getFormattedHours(day, timingData) {
  const dayHours = timingData.hours?.[day.toString()];
  
  if (!dayHours || dayHours.length === 0) {
    return "No hours available";
  }

  const allRanges = [];
  
  for (const slot of dayHours) {
    if (!slot.timing || slot.timing.length === 0) {
      return "Open 24/7";
    }
    
    for (const timeRange of slot.timing) {
      if (timeRange.length === 2 && timeRange[0] !== timeRange[1]) {
        const [start, end] = timeRange;
        allRanges.push(`${minutesToTime12Hour(start)} - ${minutesToTime12Hour(end)}`);
      }
    }
  }
  
  return allRanges.length > 0 ? allRanges.join(", ") : "Closed";
}

/**
 * Run all test scenarios
 */
function runMultipleTimingSlotsTest() {
  console.log("🧪 TESTING: Multiple Timing Slots Functionality");
  console.log("=" .repeat(60));
  
  testScenarios.forEach((scenario, index) => {
    console.log(`\n📋 Test ${index + 1}: ${scenario.description}`);
    
    const result = testBusinessHours(scenario.day, scenario.time, testTimingData);
    const formattedHours = getFormattedHours(scenario.day, testTimingData);
    
    console.log(`   Time: ${minutesToTime12Hour(scenario.time)}`);
    console.log(`   Status: ${result.isOpen ? "🟢 OPEN" : "🔴 CLOSED"}`);
    console.log(`   Reason: ${result.reason}`);
    console.log(`   Day Hours: ${formattedHours}`);
  });
  
  console.log("\n" + "=" .repeat(60));
  console.log("✅ Multiple timing slots test completed!");
  
  return {
    testData: testTimingData,
    scenarios: testScenarios,
    summary: "All test scenarios executed successfully"
  };
}

// Export for browser console usage
if (typeof window !== "undefined") {
  window.runMultipleTimingSlotsTest = runMultipleTimingSlotsTest;
  window.testTimingData = testTimingData;
  
  console.log("🔧 Multiple timing slots test functions loaded!");
  console.log("Run: window.runMultipleTimingSlotsTest() to test the functionality");
}

// Auto-run if in Node.js environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    runMultipleTimingSlotsTest,
    testTimingData,
    testScenarios
  };
}
