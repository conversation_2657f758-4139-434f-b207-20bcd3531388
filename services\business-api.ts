import type {
  BusinessLocationsResponse,
  Branch,
  BranchSettings,
  BranchTiming,
} from "@/types/business";
import { BUSINESS_ID, BRANCH_ID } from "@/constants/app-constants";

const BUSINESS_LOCATIONS_API = `https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/locations/${BRANCH_ID}`;

// Parse the branch settings from JSON string to object
function parseBranchSettings(branch: Branch): Branch {
  try {
    if (branch.settings) {
      const parsedSettings = JSON.parse(branch.settings) as BranchSettings;
      return {
        ...branch,
        parsedSettings,
      };
    }
  } catch (error) {
    console.error("Error parsing branch settings");
  }
  return branch;
}

// Simple timing validation - check if business is open right now
function validateBusinessHours(branch: Branch): boolean {
  try {
    if (!branch.timing) {
      console.log("No timing data - assuming open");
      return true;
    }

    const timingData = JSON.parse(branch.timing);
    const now = new Date();
    const currentDay = now.getDay() === 0 ? 7 : now.getDay(); // Convert Sunday (0) to 7
    const currentMinutes = now.getHours() * 60 + now.getMinutes();

    console.log(
      `Current time: Day ${currentDay}, ${now.getHours()}:${now
        .getMinutes()
        .toString()
        .padStart(2, "0")} (${currentMinutes} minutes)`
    );

    const dayHours = timingData.hours?.[currentDay.toString()];
    if (!dayHours || dayHours.length === 0) {
      console.log("No hours for today - closed");
      return false;
    }

    for (const slot of dayHours) {
      if (!slot.timing || slot.timing.length === 0) {
        console.log("Empty timing array - open 24/7");
        return true;
      }

      for (const timeRange of slot.timing) {
        if (
          timeRange.length === 2 &&
          typeof timeRange[0] === "number" &&
          typeof timeRange[1] === "number"
        ) {
          const openTime = timeRange[0];
          const closeTime = timeRange[1];

          if (openTime === closeTime) {
            console.log(`Same open/close time (${openTime}) - closed`);
            continue;
          }

          if (currentMinutes >= openTime && currentMinutes <= closeTime) {
            console.log(
              `Open: ${openTime}-${closeTime} minutes, current: ${currentMinutes}`
            );
            return true;
          } else {
            console.log(
              `Closed: ${openTime}-${closeTime} minutes, current: ${currentMinutes}`
            );
          }
        }
      }
    }

    return false;
  } catch (error) {
    console.error("Error validating business hours:", error);
    return true; // Default to open if error
  }
}

// Parse the branch timing from JSON string to object
function parseBranchTiming(branch: Branch): Branch {
  try {
    if (branch.timing) {
      const parsedTiming = JSON.parse(branch.timing) as BranchTiming;

      // Simple validation on load
      const isOpen = validateBusinessHours(branch);
      console.log(
        `Branch ${branch.id} is currently: ${isOpen ? "OPEN" : "CLOSED"}`
      );

      return {
        ...branch,
        parsedTiming,
      };
    }
  } catch (error) {
    console.error("Error parsing branch timing:", error);
  }
  return branch;
}

export async function fetchBusinessLocations(): Promise<BusinessLocationsResponse> {
  try {
    const response = await fetch(BUSINESS_LOCATIONS_API);

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = (await response.json()) as BusinessLocationsResponse;

    // Parse branch settings and timing for each branch
    if (data.result && data.result.branches) {
      console.log(
        "🏢 fetchBusinessLocations - Processing branches:",
        data.result.branches.length
      );
      data.result.branches = data.result.branches
        .map(parseBranchSettings)
        .map(parseBranchTiming);

      // Log the final parsed branches
      console.log(
        "🏢 fetchBusinessLocations - Final branches with parsed timing:",
        data.result.branches.map((b) => ({
          id: b.id,
          hasParsedTiming: !!b.parsedTiming,
          timingKeys: b.parsedTiming ? Object.keys(b.parsedTiming) : [],
          hoursKeys: b.parsedTiming?.hours
            ? Object.keys(b.parsedTiming.hours)
            : [],
        }))
      );
    }

    return data;
  } catch (error) {
    console.error("Error fetching business locations");
    throw error;
  }
}

// Get minimum spend amount from branch settings
export function getMinimumSpend(branch: Branch): number {
  if (branch.parsedSettings?.cart?.minimum_spent) {
    return Number.parseFloat(branch.parsedSettings.cart.minimum_spent);
  }
  return 0;
}

// Get formatted address for a branch
export function getFormattedBranchAddress(branch: Branch): string {
  return `${branch.address}, ${branch.city}, ${branch.location}, ${branch.country}`;
}

// Convert minutes since midnight to time string (e.g., 600 -> "10:00")
function minutesToTimeString(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, "0")}:${mins
    .toString()
    .padStart(2, "0")}`;
}

// Convert minutes since midnight to 12-hour format time string (e.g., 600 -> "10:00 AM")
function minutesToTimeString12Hour(minutes: number): string {
  const hours24 = Math.floor(minutes / 60);
  const mins = minutes % 60;

  // Convert to 12-hour format
  const hours12 = hours24 === 0 ? 12 : hours24 > 12 ? hours24 - 12 : hours24;
  const ampm = hours24 >= 12 ? "PM" : "AM";

  return `${hours12}:${mins.toString().padStart(2, "0")} ${ampm}`;
}

// Get current day number (1-7, Monday-Sunday)
function getCurrentDayNumber(): number {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
  return dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
}

// Get current time in minutes since midnight
function getCurrentTimeInMinutes(): number {
  const now = new Date();
  return now.getHours() * 60 + now.getMinutes();
}

// Check if business is open based on hours
export function isBranchOpen(branch: Branch): boolean {
  return validateBusinessHours(branch);
}

// Get business status with next opening time
export function getBranchStatus(branch: Branch): {
  isOpen: boolean;
  status: "open" | "closed" | "open_24_7";
  nextOpenTime?: string;
  todayHours?: string;
} {
  console.log(
    "📊 getBranchStatus - Starting status check for branch:",
    branch.id
  );

  if (!branch.parsedTiming) {
    console.log("⚠️ getBranchStatus - No parsed timing data, returning open");
    return { isOpen: true, status: "open" };
  }

  const currentDay = getCurrentDayNumber().toString();
  const dayTimings = branch.parsedTiming.hours?.[currentDay];

  console.log(
    "📊 getBranchStatus - Current day:",
    currentDay,
    "Day timings:",
    dayTimings
  );

  if (!dayTimings || dayTimings.length === 0) {
    console.log(
      "❌ getBranchStatus - No timing data for this day, returning closed"
    );
    return { isOpen: false, status: "closed" };
  }

  // Check if any timing slot is 24/7
  for (const timingSlot of dayTimings) {
    if (!timingSlot.timing || timingSlot.timing.length === 0) {
      console.log(
        "✅ getBranchStatus - Found 24/7 timing slot, returning open_24_7"
      );
      return { isOpen: true, status: "open_24_7" };
    }
  }

  const isOpen = isBranchOpen(branch);
  console.log("📊 getBranchStatus - isBranchOpen result:", isOpen);

  // Get today's hours for display
  let todayHours = "";
  if (dayTimings.length > 0 && dayTimings[0].timing.length > 0) {
    const timeRanges = dayTimings[0].timing;
    console.log(
      "📊 getBranchStatus - Processing time ranges for display:",
      timeRanges
    );

    const ranges = timeRanges
      .filter(
        (range) =>
          range.length === 2 &&
          typeof range[0] === "number" &&
          typeof range[1] === "number" &&
          range[0] !== range[1]
      )
      .map(
        (range) =>
          `${minutesToTimeString12Hour(
            range[0] as number
          )} - ${minutesToTimeString12Hour(range[1] as number)}`
      );
    todayHours = ranges.join(", ");
    console.log("📊 getBranchStatus - Formatted today's hours:", todayHours);
  }

  const result = {
    isOpen,
    status: (isOpen ? "open" : "closed") as "open" | "closed",
    todayHours: todayHours || undefined,
  };

  console.log("📊 getBranchStatus - Final result:", result);
  return result;
}

// Check if branch allows ordering for future delivery
export function canOrderForLater(branch: Branch): boolean {
  // This function can be extended to check business settings for future orders
  // For now, return true if branch has timing data
  return !!branch.parsedTiming;
}

// Debug function to test timing manually - call from browser console
// Usage: window.debugTiming()
export function debugTiming() {
  console.log("🔧 DEBUG: Manual timing test started");

  // Get current day and time
  const currentDay = getCurrentDayNumber();
  const currentTime = getCurrentTimeInMinutes();

  console.log(
    "🔧 DEBUG: Current day:",
    currentDay,
    "Current time (minutes):",
    currentTime
  );
  console.log(
    "🔧 DEBUG: Current time (formatted):",
    minutesToTimeString(currentTime)
  );

  // Try to get business data from window (if available)
  if (typeof window !== "undefined") {
    console.log(
      "🔧 DEBUG: Window object available, checking for business data"
    );
    // This will help us see if we can access the business context
  }

  return {
    currentDay,
    currentTime,
    currentTimeFormatted: minutesToTimeString12Hour(currentTime),
  };
}

// Make debug function available globally
if (typeof window !== "undefined") {
  (window as any).debugTiming = debugTiming;
}

// Comprehensive business hours validation utility
export function validateBusinessHoursWithMessage(
  branch: Branch | null,
  deliveryTiming: string = "now"
): {
  isValid: boolean;
  message: string;
  status: "open" | "closed" | "open_24_7" | "no_branch";
  todayHours?: string;
} {
  // Check if branch exists
  if (!branch) {
    return {
      isValid: false,
      message: "No branch selected",
      status: "no_branch",
    };
  }

  // For scheduled orders, always allow
  if (
    deliveryTiming === "later" ||
    deliveryTiming === "weekly" ||
    deliveryTiming === "biweekly"
  ) {
    return {
      isValid: true,
      message: "Scheduled order - no time restriction",
      status: "open",
    };
  }

  // For immediate orders, check business hours
  const isOpen = isBranchOpen(branch);
  const status = getBranchStatus(branch);

  if (!isOpen) {
    const hoursInfo = status.todayHours
      ? ` Today's hours: ${status.todayHours}`
      : "";
    return {
      isValid: false,
      message: `Business is currently closed.${hoursInfo} Please schedule your order for later.`,
      status: status.status,
      todayHours: status.todayHours,
    };
  }

  return {
    isValid: true,
    message: status.status === "open_24_7" ? "Open 24/7" : "Business is open",
    status: status.status,
    todayHours: status.todayHours,
  };
}
